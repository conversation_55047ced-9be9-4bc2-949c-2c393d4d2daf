<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Thêm index cho phone_number để tăng tốc độ tìm kiếm
        Schema::table('customers', function (Blueprint $table) {
            // Thêm index cho phone_number nếu chưa có
            if (!$this->hasIndex('customers', 'customers_phone_number_index')) {
                $table->index('phone_number', 'customers_phone_number_index');
            }
            
            // Thêm index cho name để tăng tốc độ search
            if (!$this->hasIndex('customers', 'customers_name_index')) {
                $table->index('name', 'customers_name_index');
            }
        });

        // Thêm index cho customer_id trong bookings
        Schema::table('bookings', function (Blueprint $table) {
            if (!$this->hasIndex('bookings', 'bookings_customer_id_index')) {
                $table->index('customer_id', 'bookings_customer_id_index');
            }
            
            // Composite index cho customer_id và customer_coming_result
            if (!$this->hasIndex('bookings', 'bookings_customer_result_index')) {
                $table->index(['customer_id', 'customer_coming_result'], 'bookings_customer_result_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropIndex('customers_phone_number_index');
            $table->dropIndex('customers_name_index');
        });

        Schema::table('bookings', function (Blueprint $table) {
            $table->dropIndex('bookings_customer_id_index');
            $table->dropIndex('bookings_customer_result_index');
        });
    }

    /**
     * Check if index exists
     */
    private function hasIndex(string $table, string $index): bool
    {
        $indexes = Schema::getConnection()
            ->getDoctrineSchemaManager()
            ->listTableIndexes($table);
            
        return array_key_exists($index, $indexes);
    }
};
