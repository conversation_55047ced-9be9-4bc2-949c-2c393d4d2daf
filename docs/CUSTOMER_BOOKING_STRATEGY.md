# Customer Booking Strategy - <PERSON><PERSON> lý thông tin khách hàng trong booking

## Tổng quan vấn đề

Trong hệ thống booking, một customer có thể có nhiều booking với thông tin khác nhau (tên, địa chỉ) nhưng cùng số điện thoại. Điều này tạo ra conflicts và có thể dẫn đến việc tạo sai hoặc ghi đè nhầm data customer.

## Chiến lược xử lý

### 1. Nguyên tắc cơ bản

- **Phone number là unique identifier chính** cho customer
- **Một phone number = một customer record**
- **Thông tin customer có thể thay đổi theo thời gian** (tên, địa chỉ)
- **Luôn log mọi thay đổi** để có thể trace back

### 2. Cá<PERSON> trường hợp xử lý

#### Case 1: Tạo booking mới
```
Input: customer data với phone number
Process:
1. Tìm customer theo phone number
2. Nếu tồn tại:
   - So s<PERSON>h thông tin mới với hiện tại
   - Update nếu có thay đổi hợp lý
   - Log changes
3. Nếu không tồn tại:
   - Tạo customer mới
   - Log creation
```

#### Case 2: Edit booking - phone number không đổi
```
Input: customer data với phone number cũ
Process:
1. Kiểm tra quyền edit customer info
2. Update thông tin nếu có quyền
3. Log changes
```

#### Case 3: Edit booking - phone number thay đổi
```
Input: customer data với phone number mới
Process:
1. Tìm customer với phone number mới
2. Nếu tồn tại:
   - Chuyển booking sang customer đã tồn tại
   - Log transfer
3. Nếu không tồn tại:
   - Kiểm tra quyền update phone number
   - Update customer hiện tại hoặc tạo mới
   - Log changes
```

### 3. Validation Rules

#### SafeCustomerPhoneNumberRule
- Kiểm tra phone number không thuộc về customer khác khi edit
- Ngăn chặn việc ghi đè data customer

#### Permissions
- `updatePhoneNumber`: Quyền thay đổi số điện thoại
- `update`: Quyền cập nhật thông tin customer

### 4. Data Normalization

#### Phone Number
- Loại bỏ ký tự không phải số
- Chuẩn hóa format Việt Nam (0xxxxxxxxx)

#### Name
- Trim whitespace
- Title case
- Loại bỏ ký tự đặc biệt

#### Address
- Trim whitespace
- Capitalize first letter

### 5. Conflict Resolution

#### Duplicate Detection
```php
// Tìm customers trùng phone number
$duplicates = Customer::where('phone_number', $phoneNumber)->get();
```

#### Merge Strategy
1. Chọn primary customer (oldest với nhiều booking nhất)
2. Chuyển tất cả bookings và revenues sang primary
3. Xóa duplicate customers
4. Log merge action

#### Data Quality Suggestions
```php
// Đề xuất data tốt nhất dựa trên frequency và recency
$suggestions = $conflictService->suggestBestCustomerData($customer);
```

### 6. Logging và Audit Trail

#### CustomerChangeLog
- Track mọi thay đổi customer data
- Ghi rõ context (booking_create, booking_edit, customer_edit)
- Store old_data và new_data
- Link với booking và user

#### Log Fields
- `customer_id`: ID customer
- `booking_id`: ID booking (nếu có)
- `user_id`: User thực hiện thay đổi
- `action`: create, update, phone_change, merge
- `context`: booking_create, booking_edit, customer_edit
- `old_data`: Data cũ
- `new_data`: Data mới
- `changed_fields`: Danh sách fields thay đổi

### 7. Commands và Tools

#### Fix Conflicts Command
```bash
# Dry run để xem conflicts
php artisan customer:fix-conflicts --dry-run

# Fix specific phone number
php artisan customer:fix-conflicts --phone=0123456789

# Fix all conflicts với limit
php artisan customer:fix-conflicts --limit=100
```

#### Data Quality Report
```php
$report = $conflictService->generateCustomerDataQualityReport();
// Returns: duplicate_phone_numbers, empty_names, empty_addresses, total_customers
```

### 8. Best Practices

#### For Developers
1. Luôn sử dụng `BookingCustomerService` thay vì direct update
2. Validate phone number với `SafeCustomerPhoneNumberRule`
3. Check permissions trước khi update customer data
4. Log mọi thay đổi quan trọng

#### For Users
1. Kiểm tra kỹ thông tin customer trước khi save
2. Không thay đổi phone number trừ khi thực sự cần thiết
3. Report conflicts cho admin khi phát hiện

#### For Admins
1. Chạy data quality report định kỳ
2. Review customer change logs thường xuyên
3. Merge duplicate customers khi phát hiện
4. Monitor validation errors

### 9. Migration Plan

#### Phase 1: Immediate Fixes
- [x] Fix updateOrCreate bug trong BookingService
- [x] Add SafeCustomerPhoneNumberRule
- [x] Create BookingCustomerService

#### Phase 2: Enhanced Logging
- [x] Create CustomerChangeLog model
- [x] Implement logging trong BookingCustomerService
- [x] Add database indexes

#### Phase 3: Conflict Resolution
- [x] Create CustomerConflictResolutionService
- [x] Create fix-conflicts command
- [x] Add data normalization

#### Phase 4: Monitoring & Maintenance
- [ ] Create admin dashboard cho customer conflicts
- [ ] Add automated alerts cho duplicate detection
- [ ] Implement customer merge UI

### 10. Testing Strategy

#### Unit Tests
- BookingCustomerService methods
- Validation rules
- Data normalization functions

#### Integration Tests
- Booking create/edit flows
- Customer merge scenarios
- Permission checks

#### Manual Testing
- Create booking với existing customer
- Edit booking với phone number change
- Merge duplicate customers
- Data quality reports

### 11. Performance Considerations

#### Database Indexes
- `customers.phone_number` (index)
- `customers.name` (index for search)
- `bookings.customer_id` (index)
- `customer_change_logs.customer_id` (index)

#### Caching
- Cache customer lookup results
- Cache permission checks
- Cache data quality metrics

#### Batch Processing
- Process conflicts in batches
- Use queues for heavy operations
- Implement progress tracking
