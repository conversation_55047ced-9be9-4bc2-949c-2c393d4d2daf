# Customer Booking Strategy - <PERSON><PERSON> lý thông tin khách hàng trong booking

## Tổng quan vấn đề

Trong hệ thống booking, một customer có thể có nhiều booking với thông tin khác nhau (tên, địa chỉ) nhưng cùng số điện thoại. Điều này tạo ra conflicts và có thể dẫn đến việc tạo sai hoặc ghi đè nhầm data customer.

## Chiến lược xử lý

### 1. Nguyên tắc cơ bản

- **Phone number là unique identifier chính** cho customer
- **Một phone number = một customer record**
- **Thông tin customer có thể thay đổi theo thời gian** (tên, địa chỉ)
- **Luôn log mọi thay đổi** để có thể trace back

### 2. Cá<PERSON> trường hợp xử lý

#### Case 1: Tạo booking mới
```
Input: customer data với phone number
Process:
1. Tìm customer theo phone number
2. Nếu tồn tại:
   - So s<PERSON>h thông tin mới với hiện tại
   - Update nếu có thay đổi hợp lý
   - Log changes
3. Nếu không tồn tại:
   - Tạo customer mới
   - Log creation
```

#### Case 2: Edit booking - phone number không đổi
```
Input: customer data với phone number cũ
Process:
1. Kiểm tra quyền edit customer info
2. Update thông tin nếu có quyền
3. Log changes
```

#### Case 3: Edit booking - phone number thay đổi
```
Input: customer data với phone number mới
Process:
1. Tìm customer với phone number mới
2. Nếu tồn tại:
   - Chuyển booking sang customer đã tồn tại
   - Log transfer
3. Nếu không tồn tại:
   - Kiểm tra quyền update phone number
   - Update customer hiện tại hoặc tạo mới
   - Log changes
```

### 3. Validation Rules

#### SafeCustomerPhoneNumberRule
- Kiểm tra phone number không thuộc về customer khác khi edit
- Ngăn chặn việc ghi đè data customer

#### Permissions
- `updatePhoneNumber`: Quyền thay đổi số điện thoại
- `update`: Quyền cập nhật thông tin customer

### 4. Data Normalization

#### Phone Number
- Loại bỏ ký tự không phải số
- Chuẩn hóa format Việt Nam (0xxxxxxxxx)

#### Name
- Trim whitespace
- Title case
- Loại bỏ ký tự đặc biệt

#### Address
- Trim whitespace
- Capitalize first letter

### 5. Smart Update Strategy

#### Update Logic
Khi có thông tin customer mới, system sẽ áp dụng logic thông minh:

```php
// Chỉ update nếu thông tin mới hợp lý hơn
protected function shouldUpdateField(Customer $customer, string $field, $newValue): bool
{
    // Nếu field hiện tại rỗng → luôn update
    // Nếu giá trị mới rỗng → không update
    // Age: chỉ update nếu tuổi mới lớn hơn
    // Name/Address: chỉ update nếu thông tin mới chi tiết hơn
}
```

#### Nguyên tắc Update
1. **Không ghi đè data có giá trị bằng data rỗng**
2. **Age chỉ tăng, không giảm** (người ta lớn lên theo thời gian)
3. **Name/Address ưu tiên thông tin chi tiết hơn** (dài hơn)
4. **Province luôn update** nếu khác nhau

### 6. Logging và Audit Trail

#### Laravel Log Integration
- Sử dụng Laravel Log system để track changes
- Log level: INFO cho customer data changes
- Structured logging với context đầy đủ

#### Log Format
```php
Log::info('Customer data changed', [
    'customer_id' => $customer->id,
    'customer_phone' => $customer->phone_number,
    'booking_id' => $booking?->id,
    'user_id' => $user->id,
    'user_name' => $user->name,
    'context' => 'booking_edit',
    'changed_fields' => ['name', 'address'],
    'changes' => [
        'name' => ['old' => 'Nguyễn A', 'new' => 'Nguyễn Văn A'],
        'address' => ['old' => 'HN', 'new' => 'Hà Nội']
    ],
    'timestamp' => '2024-01-15T10:30:00Z',
]);
```

### 7. Monitoring và Log Analysis

#### Xem Log Files
```bash
# Xem customer change logs
tail -f storage/logs/laravel.log | grep "Customer data changed"

# Filter theo customer ID
grep "customer_id.*123" storage/logs/laravel.log

# Filter theo context
grep "booking_edit" storage/logs/laravel.log | grep "Customer data changed"
```

#### Log Analysis với Laravel
```php
// Có thể tạo custom log channel cho customer changes
// config/logging.php
'channels' => [
    'customer_changes' => [
        'driver' => 'single',
        'path' => storage_path('logs/customer_changes.log'),
        'level' => 'info',
    ],
];
```

### 8. Best Practices

#### For Developers
1. Luôn sử dụng `BookingCustomerService` thay vì direct update
2. Validate phone number với `SafeCustomerPhoneNumberRule`
3. Check permissions trước khi update customer data
4. Log mọi thay đổi quan trọng

#### For Users
1. Kiểm tra kỹ thông tin customer trước khi save
2. Không thay đổi phone number trừ khi thực sự cần thiết
3. Report conflicts cho admin khi phát hiện

#### For Admins

1. Review customer change logs thường xuyên
2. Monitor validation errors và phone number changes
3. Kiểm tra CustomerChangeLog để phát hiện patterns bất thường
4. Backup database trước khi có thay đổi lớn

### 9. Migration Plan

#### Phase 1: Core Fixes ✅
- [x] Fix updateOrCreate bug trong BookingService
- [x] Add SafeCustomerPhoneNumberRule
- [x] Create BookingCustomerService

#### Phase 2: Enhanced Logging ✅
- [x] Create CustomerChangeLog model
- [x] Implement logging trong BookingCustomerService
- [x] Add database indexes

#### Phase 3: Smart Update Logic ✅
- [x] Implement shouldUpdateField logic
- [x] Add intelligent customer data merging
- [x] Prevent data overwrites with empty values

#### Phase 4: Future Enhancements
- [ ] Create admin dashboard cho customer change monitoring
- [ ] Add automated alerts cho suspicious changes
- [ ] Implement customer history view UI

### 10. Testing Strategy

#### Unit Tests
- BookingCustomerService methods
- SafeCustomerPhoneNumberRule validation
- shouldUpdateField logic

#### Integration Tests
- Booking create/edit flows với existing customers
- Phone number change scenarios
- Permission checks và field updates

#### Manual Testing Scenarios
1. **Tạo booking mới với SĐT đã tồn tại**
   - Verify auto-fill customer info
   - Test update logic khi có thông tin mới

2. **Edit booking với phone number change**
   - Test chuyển sang customer khác
   - Test validation khi SĐT thuộc customer khác

3. **Customer data updates**
   - Test smart update logic
   - Verify không ghi đè data có giá trị bằng rỗng

### 11. Performance Considerations

#### Database Indexes
- `customers.phone_number` (index) - tăng tốc lookup
- `customers.name` (index) - tăng tốc search
- `bookings.customer_id` (index) - tăng tốc join
- `customer_change_logs.customer_id` (index) - tăng tốc audit

#### Optimization
- Cache customer lookup results trong session
- Batch log writes để giảm database calls
- Index composite cho frequent queries
