<?php

namespace App\Rules\Booking;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Models\Booking;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class SafeCustomerPhoneNumberRule implements ValidationRule
{
    protected ?Booking $booking;
    protected CustomerRepositoryInterface $customerRepository;

    public function __construct(Booking $booking = null)
    {
        $this->booking = $booking;
        $this->customerRepository = app()->make(CustomerRepositoryInterface::class);
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!is_string($value)) {
            return;
        }

        // Tìm customer hiện tại của booking (nếu đang edit)
        $currentCustomer = $this->booking?->customer;
        
        // Tìm customer với phone number mới
        $existingCustomer = $this->customerRepository->findByPhoneNumber($value);

        // Nếu đang edit booking và phone number thay đổi
        if ($this->booking && $currentCustomer && $existingCustomer) {
            // Nếu phone number mới thuộc về customer khác
            if ($currentCustomer->id !== $existingCustomer->id) {
                $fail(__('messages.phone_number_belongs_to_another_customer'));
            }
        }
    }
}
