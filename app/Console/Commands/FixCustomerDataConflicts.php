<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Services\Customer\CustomerConflictResolutionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixCustomerDataConflicts extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'customer:fix-conflicts 
                            {--dry-run : Run without making changes}
                            {--phone= : Fix specific phone number}
                            {--limit=100 : Limit number of customers to process}';

    /**
     * The console command description.
     */
    protected $description = 'Fix customer data conflicts and duplicates';

    protected CustomerConflictResolutionService $conflictService;

    public function __construct(CustomerConflictResolutionService $conflictService)
    {
        parent::__construct();
        $this->conflictService = $conflictService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $specificPhone = $this->option('phone');
        $limit = (int) $this->option('limit');

        $this->info('Starting customer data conflict resolution...');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Generate quality report first
        $this->generateQualityReport();

        if ($specificPhone) {
            $this->fixSpecificPhoneNumber($specificPhone, $isDryRun);
        } else {
            $this->fixAllConflicts($limit, $isDryRun);
        }

        $this->info('Customer data conflict resolution completed!');
        return 0;
    }

    /**
     * Generate and display data quality report
     */
    protected function generateQualityReport(): void
    {
        $this->info('Generating customer data quality report...');
        
        $report = $this->conflictService->generateCustomerDataQualityReport();
        
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Customers', $report['total_customers']],
                ['Duplicate Phone Numbers', $report['duplicate_phone_numbers']],
                ['Empty Names', $report['customers_with_empty_names']],
                ['Empty Addresses', $report['customers_with_empty_addresses']],
            ]
        );
    }

    /**
     * Fix conflicts for specific phone number
     */
    protected function fixSpecificPhoneNumber(string $phoneNumber, bool $isDryRun): void
    {
        $this->info("Processing phone number: {$phoneNumber}");
        
        $duplicates = $this->conflictService->findDuplicateCustomers($phoneNumber);
        
        if ($duplicates->count() <= 1) {
            $this->info('No duplicates found for this phone number.');
            return;
        }

        $this->warn("Found {$duplicates->count()} customers with phone number {$phoneNumber}");
        
        // Show details
        $this->table(
            ['ID', 'Name', 'Address', 'Bookings Count', 'Created At'],
            $duplicates->map(function ($customer) {
                return [
                    $customer->id,
                    $customer->name,
                    $customer->address,
                    $customer->bookings()->count(),
                    $customer->created_at->format('Y-m-d H:i:s'),
                ];
            })->toArray()
        );

        if (!$isDryRun) {
            // Choose primary customer (oldest one with most bookings)
            $primaryCustomer = $duplicates->sortByDesc(function ($customer) {
                return $customer->bookings()->count() * 1000 + (strtotime('2099-01-01') - strtotime($customer->created_at));
            })->first();

            $duplicateIds = $duplicates->where('id', '!=', $primaryCustomer->id)->pluck('id')->toArray();

            if ($this->confirm("Merge all duplicates into customer ID {$primaryCustomer->id}?")) {
                $adminUser = \App\Models\User::where('role', 'R001')->first(); // Admin user
                $this->conflictService->mergeCustomers($primaryCustomer, $duplicateIds, $adminUser);
                $this->info("Successfully merged customers into ID {$primaryCustomer->id}");
            }
        }
    }

    /**
     * Fix all conflicts
     */
    protected function fixAllConflicts(int $limit, bool $isDryRun): void
    {
        $this->info("Processing up to {$limit} customers...");
        
        // Find duplicate phone numbers
        $duplicatePhones = DB::table('customers')
            ->select('phone_number', DB::raw('COUNT(*) as count'))
            ->groupBy('phone_number')
            ->having('count', '>', 1)
            ->limit($limit)
            ->get();

        $progressBar = $this->output->createProgressBar($duplicatePhones->count());
        $progressBar->start();

        foreach ($duplicatePhones as $phoneData) {
            $this->fixSpecificPhoneNumber($phoneData->phone_number, $isDryRun);
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
    }

    /**
     * Normalize customer data
     */
    protected function normalizeCustomerData(bool $isDryRun): void
    {
        $this->info('Normalizing customer data...');
        
        $customers = Customer::whereNotNull('name')->limit(1000)->get();
        
        $progressBar = $this->output->createProgressBar($customers->count());
        $progressBar->start();

        foreach ($customers as $customer) {
            $originalData = $customer->toArray();
            $normalizedData = $this->conflictService->normalizeCustomerData($originalData);
            
            $hasChanges = false;
            foreach (['name', 'phone_number', 'address'] as $field) {
                if (isset($normalizedData[$field]) && $originalData[$field] !== $normalizedData[$field]) {
                    $hasChanges = true;
                    break;
                }
            }

            if ($hasChanges && !$isDryRun) {
                $customer->update($normalizedData);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
    }
}
