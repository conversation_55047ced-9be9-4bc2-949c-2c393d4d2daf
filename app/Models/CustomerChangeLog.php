<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerChangeLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'booking_id',
        'user_id',
        'action',
        'context',
        'old_data',
        'new_data',
        'changed_fields',
        'notes',
    ];

    protected $casts = [
        'old_data' => 'array',
        'new_data' => 'array',
        'changed_fields' => 'array',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for specific actions
     */
    public function scopeAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for specific context
     */
    public function scopeContext($query, string $context)
    {
        return $query->where('context', $context);
    }

    /**
     * Get formatted changed fields
     */
    public function getFormattedChangedFieldsAttribute(): string
    {
        if (!$this->changed_fields) {
            return '';
        }

        return collect($this->changed_fields)->map(function ($field) {
            return ucfirst(str_replace('_', ' ', $field));
        })->join(', ');
    }

    /**
     * Get summary of changes
     */
    public function getChangesSummaryAttribute(): string
    {
        $summary = [];
        
        if ($this->old_data && $this->new_data) {
            foreach ($this->changed_fields ?? [] as $field) {
                $oldValue = $this->old_data[$field] ?? 'N/A';
                $newValue = $this->new_data[$field] ?? 'N/A';
                $summary[] = "{$field}: {$oldValue} → {$newValue}";
            }
        }

        return implode('; ', $summary);
    }
}
