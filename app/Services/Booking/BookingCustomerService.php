<?php

namespace App\Services\Booking;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Models\Booking;
use App\Models\Customer;
use App\Models\CustomerChangeLog;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class BookingCustomerService
{
    protected CustomerRepositoryInterface $customerRepository;

    public function __construct(CustomerRepositoryInterface $customerRepository)
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * Xử lý customer cho booking - strategy pattern dựa trên context
     */
    public function handleCustomerForBooking(
        array $customerAttributes,
        ?Booking $booking = null,
        User $user = null
    ): Customer {
        return DB::transaction(function () use ($customerAttributes, $booking, $user) {
            $phoneNumber = $customerAttributes['phone_number'];
            $existingCustomer = $this->customerRepository->findByPhoneNumber($phoneNumber);
            
            // Case 1: Tạo booking mới
            if (!$booking) {
                return $this->handleNewBookingCustomer($customerAttributes, $existingCustomer);
            }
            
            // Case 2: Edit booking hiện tại
            return $this->handleEditBookingCustomer($customerAttributes, $booking, $existingCustomer, $user);
        });
    }

    /**
     * Xử lý customer khi tạo booking mới
     */
    protected function handleNewBookingCustomer(array $customerAttributes, ?Customer $existingCustomer): Customer
    {
        if ($existingCustomer) {
            // Customer đã tồn tại - có thể update thông tin nếu cần
            return $this->updateCustomerIfNeeded($existingCustomer, $customerAttributes);
        }

        // Tạo customer mới
        return $this->customerRepository->create($customerAttributes);
    }

    /**
     * Xử lý customer khi edit booking
     */
    protected function handleEditBookingCustomer(
        array $customerAttributes,
        Booking $booking,
        ?Customer $existingCustomer,
        ?User $user
    ): Customer {
        $currentCustomer = $booking->customer;
        $phoneNumber = $customerAttributes['phone_number'];

        // Case 1: Phone number không thay đổi
        if ($currentCustomer->phone_number === $phoneNumber) {
            return $this->updateCustomerIfAllowed($currentCustomer, $customerAttributes, $user);
        }

        // Case 2: Phone number thay đổi
        return $this->handlePhoneNumberChange($customerAttributes, $currentCustomer, $existingCustomer, $user);
    }

    /**
     * Xử lý khi phone number thay đổi
     */
    protected function handlePhoneNumberChange(
        array $customerAttributes,
        Customer $currentCustomer,
        ?Customer $existingCustomer,
        ?User $user
    ): Customer {
        if ($existingCustomer) {
            // Phone number mới đã thuộc về customer khác
            // Strategy: Chuyển booking sang customer đã tồn tại
            return $existingCustomer;
        }

        // Phone number mới chưa tồn tại
        // Strategy: Update customer hiện tại với phone number mới
        if ($this->canUpdateCustomerPhoneNumber($currentCustomer, $user)) {
            $currentCustomer->update($customerAttributes);
            return $currentCustomer->refresh();
        }

        // Không có quyền update - tạo customer mới và để admin xử lý conflict
        return $this->customerRepository->create($customerAttributes);
    }

    /**
     * Update customer nếu thông tin thay đổi và có quyền
     */
    protected function updateCustomerIfAllowed(Customer $customer, array $attributes, ?User $user): Customer
    {
        // Kiểm tra quyền update customer
        if (!$user || !$user->can('update', $customer)) {
            return $customer; // Không có quyền - giữ nguyên
        }

        // Chỉ update những field được phép
        $allowedFields = $this->getAllowedUpdateFields($user);
        $updateData = array_intersect_key($attributes, array_flip($allowedFields));

        if (!empty($updateData)) {
            $customer->update($updateData);
            return $customer->refresh();
        }

        return $customer;
    }

    /**
     * Update customer nếu thông tin mới khác với hiện tại
     */
    protected function updateCustomerIfNeeded(Customer $customer, array $attributes): Customer
    {
        $needsUpdate = false;
        $updateData = [];

        foreach (['name', 'age', 'address', 'province_id'] as $field) {
            if (isset($attributes[$field]) && $customer->{$field} !== $attributes[$field]) {
                $updateData[$field] = $attributes[$field];
                $needsUpdate = true;
            }
        }

        if ($needsUpdate) {
            $customer->update($updateData);
            return $customer->refresh();
        }

        return $customer;
    }

    /**
     * Kiểm tra quyền update phone number
     */
    protected function canUpdateCustomerPhoneNumber(Customer $customer, ?User $user): bool
    {
        if (!$user) {
            return false;
        }

        return $user->can('updatePhoneNumber', $customer);
    }

    /**
     * Lấy danh sách field được phép update
     */
    protected function getAllowedUpdateFields(?User $user): array
    {
        $baseFields = ['name', 'age', 'address', 'province_id'];

        if ($user && $user->can('updatePhoneNumber', Customer::class)) {
            $baseFields[] = 'phone_number';
        }

        return $baseFields;
    }

    /**
     * Tạo customer history log để track changes
     */
    public function logCustomerChange(
        Customer $customer,
        array $oldData,
        array $newData,
        User $user,
        string $context,
        ?Booking $booking = null
    ): void {
        $changedFields = [];
        $filteredOldData = [];
        $filteredNewData = [];

        // Tìm các field thay đổi
        foreach ($newData as $field => $newValue) {
            $oldValue = $oldData[$field] ?? null;
            if ($oldValue !== $newValue) {
                $changedFields[] = $field;
                $filteredOldData[$field] = $oldValue;
                $filteredNewData[$field] = $newValue;
            }
        }

        // Chỉ log nếu có thay đổi
        if (!empty($changedFields)) {
            CustomerChangeLog::create([
                'customer_id' => $customer->id,
                'booking_id' => $booking?->id,
                'user_id' => $user->id,
                'action' => $this->determineAction($changedFields, $context),
                'context' => $context,
                'old_data' => $filteredOldData,
                'new_data' => $filteredNewData,
                'changed_fields' => $changedFields,
                'notes' => $this->generateChangeNotes($changedFields, $context),
            ]);
        }
    }

    /**
     * Xác định action dựa trên changed fields và context
     */
    protected function determineAction(array $changedFields, string $context): string
    {
        if (in_array('phone_number', $changedFields)) {
            return 'phone_change';
        }

        if ($context === 'booking_create') {
            return 'create';
        }

        return 'update';
    }

    /**
     * Tạo notes mô tả thay đổi
     */
    protected function generateChangeNotes(array $changedFields, string $context): string
    {
        $fieldNames = [
            'phone_number' => 'Số điện thoại',
            'name' => 'Tên khách hàng',
            'age' => 'Tuổi',
            'address' => 'Địa chỉ',
            'province_id' => 'Tỉnh/Thành phố',
        ];

        $changedFieldNames = array_map(function ($field) use ($fieldNames) {
            return $fieldNames[$field] ?? $field;
        }, $changedFields);

        $contextNames = [
            'booking_create' => 'tạo booking',
            'booking_edit' => 'chỉnh sửa booking',
            'customer_edit' => 'chỉnh sửa thông tin khách hàng',
        ];

        $contextName = $contextNames[$context] ?? $context;

        return "Thay đổi " . implode(', ', $changedFieldNames) . " khi " . $contextName;
    }
}
