<?php

namespace App\Services\Booking;

use App\Contracts\Repositories\BookingRepositoryInterface;
use App\Contracts\Repositories\BookingSourceRepositoryInterface;
use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Contracts\Repositories\DoctorRepositoryInterface;
use App\Contracts\Repositories\ExpectedPriceRepositoryInterface;
use App\Contracts\Repositories\FacebookPageRepositoryInterface;
use App\Contracts\Repositories\ProductRepositoryInterface;
use App\Contracts\Repositories\ProvinceRepositoryInterface;
use App\Contracts\Repositories\ShopRepositoryInterface;
use App\Contracts\Repositories\UserRepositoryInterface;
use App\Enums\Booking\BookingTypeEnum;
use App\Enums\Booking\CustomerComingResultEnum;
use App\Enums\BookingSource\BookingSourceTypeEnum;
use App\Enums\Customer\AgeRangeEnum;
use App\Enums\Product\TypeEnum;
use App\Http\Requests\Booking\StoreUpdateBookingRequest;
use App\Http\Requests\Customer\CreateBookingRequest;
use App\Models\Booking;
use App\Models\Customer;
use App\Models\User;
use App\Services\BaseService;
use App\Services\Booking\BookingCustomerService;
use App\Services\Traits\HasShopIdsByUser;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class BookingService extends BaseService
{
    use HasShopIdsByUser;

    protected BookingRepositoryInterface $bookingRepository;
    protected CustomerRepositoryInterface $customerRepository;
    protected ProductRepositoryInterface $productRepository;
    protected ExpectedPriceRepositoryInterface $expectedPriceRepository;
    protected FacebookPageRepositoryInterface $facebookPageRepository;
    protected ShopRepositoryInterface $shopRepository;
    protected BookingSourceRepositoryInterface $bookingSourceRepository;
    protected ProvinceRepositoryInterface $provinceRepository;
    protected DoctorRepositoryInterface $doctorRepository;
    protected UserRepositoryInterface $userRepository;
    protected BookingCustomerService $bookingCustomerService;

    public function __construct(
        BookingRepositoryInterface       $bookingRepository,
        CustomerRepositoryInterface      $customerRepository,
        ProductRepositoryInterface       $productRepository,
        ExpectedPriceRepositoryInterface $expectedPriceRepository,
        FacebookPageRepositoryInterface  $facebookPageRepository,
        ShopRepositoryInterface          $shopRepository,
        BookingSourceRepositoryInterface $bookingSourceRepository,
        ProvinceRepositoryInterface      $provinceRepository,
        DoctorRepositoryInterface        $doctorRepository,
        UserRepositoryInterface          $userRepository,
        BookingCustomerService           $bookingCustomerService
    )
    {
        $this->bookingRepository = $bookingRepository;
        $this->customerRepository = $customerRepository;
        $this->productRepository = $productRepository;
        $this->expectedPriceRepository = $expectedPriceRepository;
        $this->facebookPageRepository = $facebookPageRepository;
        $this->shopRepository = $shopRepository;
        $this->bookingSourceRepository = $bookingSourceRepository;
        $this->provinceRepository = $provinceRepository;
        $this->doctorRepository = $doctorRepository;
        $this->userRepository = $userRepository;
        $this->bookingCustomerService = $bookingCustomerService;
    }

    public function getPaginatorByUser(int $perPage, User $user, array $conditions = []): Paginator
    {
        $salesmanIds = $this->getBookingSalesmanIdsByUser($user);
        $shopIds = $this->getShopIdsByUser($user);

        if ($user->isSaleManager() || $user->isSalesman() || $user->isMarketingLeader() || $user->isAccountantBO()) {
            $shopIds = $user->shops->pluck('id')->toArray();
        }

        if (($user->isMarketingLeader() || $user->isTPMKT()) && !isset($conditions['type'])) {
            $shopIds = $salesmanIds = null;
            $conditions = $this->detectConditionWhenUserIsMKTRole($user, $conditions);
        }

        return $this->bookingRepository->getPaginator(
            $perPage,
            $conditions,
            $salesmanIds,
            $shopIds
        );
    }

    public function getListByDateInMonth(User $user, array $conditions = []): Collection
    {
        $salesmanIds = $this->getBookingSalesmanIdsByUser($user);
        $shopIds = $this->getShopIdsByUser($user);

        if ($user->isSaleManager() || $user->isSalesman() || $user->isAccountantBO()) {
            $shopIds = $user->shops->pluck('id')->toArray();
        }

        if ($user->isMarketingLeader() || $user->isTPMKT()) {
            $shopIds = $salesmanIds = null;
            $conditions = $this->detectConditionWhenUserIsMKTRole($user, $conditions);
        }

        return $this->bookingRepository->getList(
            $conditions,
            $salesmanIds,
            $shopIds
        );
    }

    private function detectConditionWhenUserIsMKTRole(User $user, array $conditions): array
    {
        $userBookingSources = $user->bookingSources->pluck('id')->toArray();

        if(empty($conditions['booking_source_ids']) && empty($conditions['sub_booking_source_ids'])) {
            $conditions['booking_source_ids'] = $conditions['sub_booking_source_ids'] = $userBookingSources;

            return $conditions;
        }

        $conditions['booking_source_ids'] = array_intersect(
            $conditions['booking_source_ids'] ?? [],
            $userBookingSources
        );

        $conditions['sub_booking_source_ids'] = array_intersect(
            $conditions['sub_booking_source_ids'] ?? $conditions['booking_source_ids'] ?? [],
            $userBookingSources
        );
        
        return $conditions;
    }

    public function getBookingSalesmanIdsByUser(User $user): ?array
    {
        return null;
    }

    public function superStoreOrUpdate(
        User                      $creatorOrUpdater,
        StoreUpdateBookingRequest $request,
        Booking                   $booking = null
    ): Booking
    {
        DB::transaction(function () use ($request, &$booking, $creatorOrUpdater) {
            $attributes = $this->prepareAttributesBeforeStoreOrUpdate($request->getBooking());
            $bookingProductIds = $this->prepareAdditionProductIdsBeforeStoreOrUpdate($request->getBookingProductIds());

            if (!empty($customerAttributes = $request->getCustomer())) {
                $customer = $this->bookingCustomerService->handleCustomerForBooking(
                    $customerAttributes,
                    $booking,
                    $creatorOrUpdater
                );

                $attributes = array_merge($attributes, [
                    'customer_id' => $customer->getKey(),
                ]);
            }

            if ($booking) {
                $booking = $this->update(
                    $creatorOrUpdater,
                    $booking,
                    $attributes,
                    $bookingProductIds
                );
            } else {
                $attributes['creator_id'] = $creatorOrUpdater->getKey();

                $booking = $this->store(
                    $creatorOrUpdater,
                    $attributes,
                    $bookingProductIds
                );
            }
        });

        return $booking;
    }

    protected function prepareAttributesBeforeStoreOrUpdate(array $attributes): array
    {
        if (isset($attributes['facebook_page_id']) && strlen($attributes['facebook_page_id'])) {
            $attributes['facebook_page_id'] = $this->facebookPageRepository
                ->firstOrCreate([
                    'name' => $attributes['facebook_page_id'],
                ])->getKey();
        }

        if (isset($attributes['expected_price_id']) && strlen($attributes['expected_price_id'])) {
            $attributes['expected_price_id'] = $this->expectedPriceRepository
                ->firstOrCreate([
                    'name' => $attributes['expected_price_id'],
                ])->getKey();
        }

        return $attributes;
    }

    protected function prepareAdditionProductIdsBeforeStoreOrUpdate($productIds): ?array
    {
        if (!is_array($productIds)) {
            return $productIds;
        }

        return collect($productIds)
            ->filter(function ($productId) {
                return is_string($productId) && strlen($productId);
            })->map(function ($productId) {
                return $this->productRepository->firstOrCreate([
                    'name' => $productId,
                    'type' => TypeEnum::Addition,
                ])->getKey();
            })->toArray();
    }

    public function store(
        User  $creator,
        array $attributes,
        array $bookingProductIds = []
    ): Booking
    {
        if (in_array($attributes['type'], [
            BookingTypeEnum::New,
            BookingTypeEnum::TransferAnotherShop,
            BookingTypeEnum::FailedCareAgain,
            BookingTypeEnum::UpsaleCustomer,
            BookingTypeEnum::SampleRecruitment
        ])) {
            $attributes = array_merge($attributes, [
                'salesman_user_id' => $creator->getKey(),
            ]);
        }
        $booking = $this->bookingRepository->create($attributes);

        if (!empty($bookingProductIds)) {
            $booking->additionProducts()->sync($bookingProductIds);
        }

        if (!empty($attributes['booking_source_ids'])) {
            $booking->bookingSources()->syncWithPivotValues($attributes['booking_source_ids'], ['type' => BookingSourceTypeEnum::Main]);
        }

        if (!empty($attributes['sub_booking_source_ids'])) {
            $booking->subBookingSources()->syncWithPivotValues($attributes['sub_booking_source_ids'], ['type' => BookingSourceTypeEnum::Sub]);
        }

        return $booking;
    }

    public function update(
        User    $updater,
        Booking $booking,
        array   $attributes,
        ?array  $bookingProductIds = null
    ): Booking
    {
        if ($updater->isReceptionist()) {
            if (isset($attributes['customer_coming_result']) && $booking->customer_coming_result != $attributes['customer_coming_result']) {
                $attributes = array_merge($attributes, [
                    'receptionist_updated_count' => $booking->receptionist_updated_count + 1,
                ]);
            }

            if (!$booking->receptionist_user_id) {
                $attributes['receptionist_user_id'] = $updater->getKey();
            }
        }

        $booking->update($attributes);

        if (isset($bookingProductIds)) {
            $booking->additionProducts()->sync($bookingProductIds);
        }

        if (!empty($attributes['booking_source_ids'])) {
            $booking->bookingSources()->syncWithPivotValues($attributes['booking_source_ids'], ['type' => BookingSourceTypeEnum::Main]);
        }
        if (!empty($attributes['sub_booking_source_ids'])) {
            $booking->subBookingSources()->syncWithPivotValues($attributes['sub_booking_source_ids'], ['type' => BookingSourceTypeEnum::Sub]);
        }

        if (isset($attributes['booking_source_ids']) && empty($attributes['booking_source_ids'])) {
            $booking->bookingSources()->detach();
        }

        if (isset($attributes['sub_booking_source_ids']) && empty($attributes['sub_booking_source_ids'])) {
            $booking->subBookingSources()->detach();
        }

        return $booking->refresh();
    }

    public function getFilters(User $user): array
    {
        if ($user->isRoleShopGroup()) {
            $shops = $user->shops;
            $shops = $shops->count() > 1 ? $shops : null;
        } else {
            $shops = $this->shopRepository->all();
        }

        return [
            'shops' => $shops,
            'customer_coming_results' => CustomerComingResultEnum::getDescriptions(),
            'booking_sources' => $this->bookingSourceRepository->all(),
            'products' => $this->productRepository->allMainProducts(),
            'province' => $this->provinceRepository->all(),
            'age_ranges' => AgeRangeEnum::getDescriptions(),
            'expected_price' => $this->expectedPriceRepository->all(),
            'doctors' => $this->doctorRepository->all(),
            'type' => BookingTypeEnum::getDescriptions()
        ];
    }

    public function createReExaminationBooking(User $user, Customer $customer, array $attributes)
    {
        $firstBooking = $customer->firstNewBooking;
        if($firstBooking) {
            // sale
            $attributes['expected_price_id'] = $firstBooking->expected_price_id;
            $attributes['price'] = $firstBooking->price;
            $attributes['salesman_note'] = $firstBooking->salesman_note;
            $attributes['sub_salesman_user_id'] = $firstBooking->sub_salesman_user_id;
            $attributes['salesman_user_id'] = $firstBooking->salesman_user_id;

            // customer
            $attributes['customer_coming_result'] = $firstBooking->customer_coming_result;
            $attributes['customer_coming_way'] = $firstBooking->customer_coming_way;
            $attributes['facebook_page_id'] = $firstBooking->facebook_page_id;
            $attributes['booking_source_ids'] = $firstBooking->bookingSources->pluck('id')->toArray();
            $attributes['customer_friend_name'] = $firstBooking->customer_friend_name;
        }
        $attributes['type'] = BookingTypeEnum::ReExamination;
        $attributes['customer_id'] = $customer->id;
        $attributes['creator_id'] = $user->getKey();
        $attributes['customer_coming_result'] = CustomerComingResultEnum::Serviced;


        $this->store($user, $attributes);
    }
}
