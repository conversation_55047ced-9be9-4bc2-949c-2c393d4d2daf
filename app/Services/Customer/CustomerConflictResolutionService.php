<?php

namespace App\Services\Customer;

use App\Contracts\Repositories\CustomerRepositoryInterface;
use App\Models\Booking;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Service xử lý conflicts khi customer có thông tin khác nhau giữa các booking
 */
class CustomerConflictResolutionService
{
    protected CustomerRepositoryInterface $customerRepository;

    public function __construct(CustomerRepositoryInterface $customerRepository)
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * Phát hiện conflicts trong customer data
     */
    public function detectCustomerConflicts(Customer $customer): array
    {
        $bookings = $customer->bookings()->with('customer')->get();
        $conflicts = [];

        if ($bookings->count() <= 1) {
            return $conflicts;
        }

        // So sánh thông tin customer giữa các booking
        $baseCustomer = $customer;
        $fields = ['name', 'age', 'address', 'province_id'];

        foreach ($bookings as $booking) {
            $bookingCustomer = $booking->customer;
            
            foreach ($fields as $field) {
                if ($baseCustomer->{$field} !== $bookingCustomer->{$field}) {
                    $conflicts[] = [
                        'field' => $field,
                        'booking_id' => $booking->id,
                        'current_value' => $baseCustomer->{$field},
                        'booking_value' => $bookingCustomer->{$field},
                        'booking_date' => $booking->booking_date_at,
                    ];
                }
            }
        }

        return $conflicts;
    }

    /**
     * Tìm customers trùng lặp theo phone number
     */
    public function findDuplicateCustomers(string $phoneNumber): Collection
    {
        return Customer::where('phone_number', $phoneNumber)->get();
    }

    /**
     * Merge customers - gộp nhiều customer records thành một
     */
    public function mergeCustomers(Customer $primaryCustomer, array $duplicateCustomerIds, User $user): Customer
    {
        return DB::transaction(function () use ($primaryCustomer, $duplicateCustomerIds, $user) {
            $duplicateCustomers = Customer::whereIn('id', $duplicateCustomerIds)->get();
            
            foreach ($duplicateCustomers as $duplicateCustomer) {
                // Chuyển tất cả bookings sang primary customer
                $duplicateCustomer->bookings()->update(['customer_id' => $primaryCustomer->id]);
                
                // Chuyển tất cả customer revenues sang primary customer
                $duplicateCustomer->customerRevenues()->update(['customer_id' => $primaryCustomer->id]);
                
                // Log merge action
                $this->logMergeAction($primaryCustomer, $duplicateCustomer, $user);
                
                // Xóa duplicate customer
                $duplicateCustomer->delete();
            }

            return $primaryCustomer->refresh();
        });
    }

    /**
     * Đề xuất customer data tốt nhất dựa trên multiple sources
     */
    public function suggestBestCustomerData(Customer $customer): array
    {
        $bookings = $customer->bookings()->orderBy('created_at', 'desc')->get();
        $suggestions = [];

        if ($bookings->isEmpty()) {
            return $customer->toArray();
        }

        // Lấy thông tin từ booking mới nhất
        $latestBooking = $bookings->first();
        $suggestions['name'] = $customer->name;
        $suggestions['age'] = $customer->age;
        $suggestions['address'] = $customer->address;
        $suggestions['province_id'] = $customer->province_id;

        // Phân tích patterns trong data
        $nameFrequency = [];
        $addressFrequency = [];

        foreach ($bookings as $booking) {
            // Count name frequency
            $name = trim($booking->customer->name ?? '');
            if ($name) {
                $nameFrequency[$name] = ($nameFrequency[$name] ?? 0) + 1;
            }

            // Count address frequency
            $address = trim($booking->customer->address ?? '');
            if ($address) {
                $addressFrequency[$address] = ($addressFrequency[$address] ?? 0) + 1;
            }
        }

        // Suggest most frequent name
        if (!empty($nameFrequency)) {
            $suggestions['name'] = array_keys($nameFrequency, max($nameFrequency))[0];
        }

        // Suggest most frequent address
        if (!empty($addressFrequency)) {
            $suggestions['address'] = array_keys($addressFrequency, max($addressFrequency))[0];
        }

        // Suggest most recent age (people get older)
        $recentAges = $bookings->take(3)->pluck('customer.age')->filter()->values();
        if ($recentAges->isNotEmpty()) {
            $suggestions['age'] = $recentAges->max();
        }

        return $suggestions;
    }

    /**
     * Chuẩn hóa customer data
     */
    public function normalizeCustomerData(array $customerData): array
    {
        // Chuẩn hóa tên
        if (isset($customerData['name'])) {
            $customerData['name'] = $this->normalizeName($customerData['name']);
        }

        // Chuẩn hóa phone number
        if (isset($customerData['phone_number'])) {
            $customerData['phone_number'] = $this->normalizePhoneNumber($customerData['phone_number']);
        }

        // Chuẩn hóa address
        if (isset($customerData['address'])) {
            $customerData['address'] = $this->normalizeAddress($customerData['address']);
        }

        return $customerData;
    }

    /**
     * Chuẩn hóa tên khách hàng
     */
    protected function normalizeName(string $name): string
    {
        // Loại bỏ khoảng trắng thừa
        $name = preg_replace('/\s+/', ' ', trim($name));
        
        // Viết hoa chữ cái đầu mỗi từ
        return mb_convert_case($name, MB_CASE_TITLE, 'UTF-8');
    }

    /**
     * Chuẩn hóa số điện thoại
     */
    protected function normalizePhoneNumber(string $phoneNumber): string
    {
        // Loại bỏ tất cả ký tự không phải số
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Chuẩn hóa format Việt Nam
        if (strlen($phoneNumber) === 10 && substr($phoneNumber, 0, 1) === '0') {
            return $phoneNumber;
        }
        
        if (strlen($phoneNumber) === 9) {
            return '0' . $phoneNumber;
        }
        
        return $phoneNumber;
    }

    /**
     * Chuẩn hóa địa chỉ
     */
    protected function normalizeAddress(string $address): string
    {
        // Loại bỏ khoảng trắng thừa
        $address = preg_replace('/\s+/', ' ', trim($address));
        
        // Viết hoa chữ cái đầu câu
        return ucfirst(strtolower($address));
    }

    /**
     * Log merge action
     */
    protected function logMergeAction(Customer $primaryCustomer, Customer $duplicateCustomer, User $user): void
    {
        // TODO: Implement logging merge action
        // Có thể sử dụng CustomerChangeLog hoặc tạo riêng merge log
    }

    /**
     * Tạo report về customer data quality
     */
    public function generateCustomerDataQualityReport(): array
    {
        $duplicatePhones = DB::table('customers')
            ->select('phone_number', DB::raw('COUNT(*) as count'))
            ->groupBy('phone_number')
            ->having('count', '>', 1)
            ->get();

        $emptyNames = Customer::whereNull('name')->orWhere('name', '')->count();
        $emptyAddresses = Customer::whereNull('address')->orWhere('address', '')->count();

        return [
            'duplicate_phone_numbers' => $duplicatePhones->count(),
            'customers_with_empty_names' => $emptyNames,
            'customers_with_empty_addresses' => $emptyAddresses,
            'total_customers' => Customer::count(),
        ];
    }
}
