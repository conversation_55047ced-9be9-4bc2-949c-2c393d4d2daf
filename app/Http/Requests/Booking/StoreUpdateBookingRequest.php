<?php

namespace App\Http\Requests\Booking;

use App\Enums\Booking\BookingTypeEnum;
use App\Enums\Booking\CustomerComingResultEnum;
use App\Enums\Booking\CustomerComingWayEnum;
use App\Http\Requests\Customer\CustomerRequest;
use App\Http\Requests\HasDateFieldRequest;
use App\Http\Requests\HasMoneyFieldRequest;
use App\Rules\Booking\ExistedCustomerByPhoneNumberRule;
use App\Rules\Booking\SafeCustomerPhoneNumberRule;
use App\Rules\Common\ExistsDoctorIdRule;
use App\Rules\Common\ExistsShopIdRule;
use App\Rules\Common\ExistsUserIdRule;
use App\Services\Booking\BookingCreateEditViewService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreUpdateBookingRequest extends BookingRequest
{
    use HasMoneyFieldRequest, HasDateFieldRequest;

    public function rules(): array
    {
        $disabledFields = app()->call([$this, 'allDisabledFields']);
        $hideFields = $this->getHideFields();
        $rules = [];

        foreach ($this->allRules() as $field => $rule) {
            // Bỏ qua disabled fields
            if (in_array($field, $disabledFields)) {
                continue;
            }

            // Bỏ qua tất cả hidden fields, NHƯNG không bỏ qua field 'type' khi tạo mới
            if (isset($hideFields[$field]) && $hideFields[$field]) {
                $booking = $this->route('booking');
                $isEdit = (bool)$booking;

                // Field 'type' bắt buộc khi tạo mới, không được skip
                if ($field === 'type' && !$isEdit) {
                    // Không skip field type khi tạo mới
                } else {
                    continue;
                }
            }

            // Bỏ qua booking_source_ids nếu source bị ẩn
            if ($field === 'booking_source_ids' && isset($hideFields['source']) && $hideFields['source']) {
                continue;
            }

            $rules[$field] = $rule;
        }

        return $rules;
    }

    private function getHideFields(): array
    {
        $user = staff();
        $booking = $this->route('booking');
        $isEdit = (bool)$booking;
        $hideFields = [];

        // Logic giống như trong BookingCreateEditViewService
        if ($user->isShopManager() || $user->isShopLeader()) {
            $hideFields['source'] = true;
            $hideFields['facebook_page_id'] = true;
        }

        if ($user->isReceptionist()) {
            $hideFields['facebook_page_id'] = true;
            $hideFields['type'] = true;
        }

        // TpMkt role có thể có hidden fields khác
        if ($user->isTPMKT() && $isEdit) {
            $hideFields['type'] = true;
        }

        // Debug log để xem hidden fields
        \Log::info('getHideFields() result', [
            'user_id' => $user->id,
            'is_edit' => $isEdit,
            'hide_fields' => $hideFields,
        ]);

        return $hideFields;
    }

    public function prepareForValidation(): void
    {
        $this->prepareTimeForValidation([
            'booking_time_start_at',
            'booking_time_end_at',
        ]);

        $this->prepareMoneyFieldsForValidation('price');

        $this->prepareDateForValidation([
            'booking_created_at',
            'booking_date_at',
        ]);

        if (is_null($this->input('sub_booking_source_ids'))) {
            $this->merge(['sub_booking_source_ids' => []]);
        }

        if (is_null($this->input('booking_source_ids'))) {
            $this->merge(['booking_source_ids' => []]);
        }
    }

    public function getBooking(): array
    {
        $validated = $this->validated();

        // Đảm bảo field type luôn có giá trị
        if (!isset($validated['type'])) {
            if ($this->has('type') && $this->input('type') !== null) {
                // Nếu có trong input và không null, sử dụng giá trị đó
                $validated['type'] = $this->input('type');
            } else {
                // Nếu không có hoặc null, sử dụng giá trị mặc định
                $validated['type'] = \App\Enums\Booking\BookingTypeEnum::New; // Default = 1
            }
        }

        // Debug để xem validated data
        \Log::info('getBooking() validated data', [
            'user_id' => auth()->id(),
            'booking_id' => $this->route('booking')?->id,
            'validated_data' => $validated,
            'has_type' => isset($validated['type']),
            'type_value' => $validated['type'] ?? 'NOT_SET',
            'input_type' => $this->input('type'),
        ]);

        return $validated;
    }

    public function getCustomer(): array
    {
        return $this->validated('customer', []);
    }

    public function getBookingProductIds(): ?array
    {
        $disabledFields = app()->call([$this, 'allDisabledFields']);

        if (in_array('booking_product_ids', $disabledFields)) {
            return $this->validated('booking_product_ids');
        }

        return $this->validated('booking_product_ids', []);
    }

    protected function allRules(): array
    {
        $user = Auth::user();

        $rules = [
            'shop_id' => [
                'required',
                new ExistsShopIdRule(),
            ],
            'booking_created_at' => [
                'required',
                'date_format:' . config('common.datetime.format.database.date'),
            ],
            'booking_date_at' => [
                'required',
                'date_format:' . config('common.datetime.format.database.date'),
            ],
            'booking_time_start_at' => [
                'required',
                'date_format:' . config('common.datetime.format.database.time'),
            ],
            'booking_time_end_at' => [
                'required',
                'date_format:' . config('common.datetime.format.database.time'),
                'after:booking_time_start_at',
            ],

            'customer' => [
                'required',
                'array',
            ],
            'customer.phone_number' => [
                'required',
                'regex:' . CustomerRequest::PHONE_NUMBER_REGEX,
                'min:' . CustomerRequest::PHONE_NUMBER_MIN_LENGTH,
                'max:' . CustomerRequest::PHONE_NUMBER_MAX_LENGTH,
                new SafeCustomerPhoneNumberRule($this->route('booking')),
            ],
            'customer.name' => [
                'required',
                'string',
                'max:' . CustomerRequest::NAME_MAX_LENGTH,
            ],
            'customer.age' => [
                'nullable',
                'integer',
                'min:' . CustomerRequest::AGE_MIN,
                'max:' . CustomerRequest::AGE_MAX,
            ],
            'customer.province_id' => [
                'nullable',
            ],
            'customer.address' => [
                'required',
                'string',
                'max:' . CustomerRequest::ADDRESS_MAX_LENGTH,
            ],
            'customer_friend_name' => [
                'nullable',
                'string',
                'max:' . self::CUSTOMER_FRIEND_NAME_MAX_LENGTH,
            ],
            'facebook_page_id' => [
                'nullable',
            ],
            'customer_coming_way' => [
                'required',
                Rule::in(CustomerComingWayEnum::getValues()),
            ],
            'customer_coming_result' => [
                'required',
                Rule::in(CustomerComingResultEnum::getValuesForUser(staff())),
            ],

            'product_id' => [
                'required',
            ],
            'booking_product_ids' => [
                'nullable',
                'array'
            ],
            'booking_product_ids.*' => [
                'required',
            ],

            'salesman_user_id' => [
                (!$user->isAdministrator() && !$user->isShopLeader() && !$user->isShopManager() && !$user->isReceptionist()) ? 'required' : 'nullable',
            ],
            'sub_salesman_user_id' => [
                'nullable',
            ],
            'expected_price_id' => [
                'required',
            ],
            'salesman_note' => [
                'nullable',
                'string',
                'max:' . self::SALESMAN_NOTE_MAX_LENGTH,
            ],

            'price' => [
                'nullable',
                'integer',
                'min:' . self::PRICE_MIN,
                'max:' . self::PRICE_MAX,
            ],
            'shop_note' => [
                'nullable',
                'string',
                'max:' . self::SHOP_NOTE_MAX_LENGTH,
            ],
            'receptionist_user_id' => [
                'nullable',
                new ExistsUserIdRule(),
            ],
            'type' => [
                'required',
                'integer',
                Rule::in(BookingTypeEnum::getAllowTypeByUser($user)),
            ],
            'doctor_name' => [
                'nullable',
                'string',
                'max:' . self::CUSTOMER_FRIEND_NAME_MAX_LENGTH,
            ],
            'doctor_assistant_name' => [
                'nullable',
                'string',
                'max:' . self::CUSTOMER_FRIEND_NAME_MAX_LENGTH,
            ],
            'doctor_id' => [
                'nullable',
                new ExistsDoctorIdRule(),
            ],
        ];

        // Xử lý booking_source_ids rules
        $inputType = $this->input('type');
        $booking = $this->route('booking');

        // Nếu field type không được gửi lên, sử dụng type hiện tại (khi edit) hoặc null (khi tạo mới)
        if ($inputType === null && $booking) {
            $inputType = $booking->type;
        }

        if (($user->isShopManager() || $user->isReceptionist() || $user->isShopLeader())
            && $inputType && in_array($inputType, [BookingTypeEnum::Passersby, BookingTypeEnum::Referral, BookingTypeEnum::FailedCareAgain, BookingTypeEnum::SampleRecruitment])
        ) {
            $rules['booking_source_ids'] = [
                'nullable',
                'array',
            ];
        } else {
            $rules['booking_source_ids'] = [
                'required',
                'array',
            ];
        }

        $rules['sub_booking_source_ids'] = [
            'nullable',
            'array',
        ];

        return $rules;
    }

    public function allDisabledFields(BookingCreateEditViewService $bookingCreateEditViewService): array
    {
        return $bookingCreateEditViewService->getDisabledFieldsByUser(staff(), $this->route('booking'));
    }
}
